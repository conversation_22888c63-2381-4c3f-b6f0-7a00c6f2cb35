'use client'

import { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { Upload, X, Image as ImageIcon } from 'lucide-react'

interface ImageUploadProps {
  onImageSelect: (file: File, base64: string) => void
  selectedImage: File | null
  onRemoveImage: () => void
}

export default function ImageUpload({ onImageSelect, selectedImage, onRemoveImage }: ImageUploadProps) {
  const [preview, setPreview] = useState<string | null>(null)

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (file) {
      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        alert('File size must be less than 10MB')
        return
      }

      // Validate file type
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file')
        return
      }

      // Create preview
      const reader = new FileReader()
      reader.onload = (e) => {
        const result = e.target?.result as string
        setPreview(result)
        onImageSelect(file, result)
      }
      reader.readAsDataURL(file)
    }
  }, [onImageSelect])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp', '.gif']
    },
    multiple: false,
    maxSize: 10 * 1024 * 1024, // 10MB
  })

  const handleRemove = () => {
    setPreview(null)
    onRemoveImage()
  }

  if (selectedImage && preview) {
    return (
      <div className="relative">
        <div className="relative bg-gray-50 rounded-lg overflow-hidden">
          <img
            src={preview}
            alt="Selected image"
            className="w-full h-64 object-cover"
          />
          <button
            onClick={handleRemove}
            className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
          >
            <X size={16} />
          </button>
        </div>
        <div className="mt-2 text-sm text-gray-600 text-center">
          {selectedImage.name} ({(selectedImage.size / 1024 / 1024).toFixed(2)} MB)
        </div>
      </div>
    )
  }

  return (
    <div
      {...getRootProps()}
      className={`
        border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
        ${isDragActive 
          ? 'border-blue-400 bg-blue-50' 
          : 'border-gray-300 hover:border-gray-400'
        }
      `}
    >
      <input {...getInputProps()} />
      
      <div className="flex flex-col items-center gap-4">
        <div className={`
          p-4 rounded-full 
          ${isDragActive ? 'bg-blue-100' : 'bg-gray-100'}
        `}>
          {isDragActive ? (
            <Upload className="w-8 h-8 text-blue-500" />
          ) : (
            <ImageIcon className="w-8 h-8 text-gray-400" />
          )}
        </div>
        
        <div>
          <p className="text-lg font-medium text-gray-900">
            {isDragActive ? 'Drop your image here' : 'Upload a photo'}
          </p>
          <p className="text-sm text-gray-500 mt-1">
            Drag and drop or click to select
          </p>
          <p className="text-xs text-gray-400 mt-2">
            Supports: JPEG, PNG, WebP, GIF (max 10MB)
          </p>
        </div>
      </div>
    </div>
  )
}
