'use client'

import { Calendar, Heart, TrendingUp, Lightbulb, Users, Eye } from 'lucide-react'
import { AgeAnalysisResult, EmotionAnalysisResult } from '@/lib/openai'
import { AnalysisType } from '@/lib/credits'

interface AnalysisResultsProps {
  result: AgeAnalysisResult | EmotionAnalysisResult
  analysisType: AnalysisType
  isFreeTier: boolean
}

export default function AnalysisResults({ result, analysisType, isFreeTier }: AnalysisResultsProps) {
  if (analysisType === 'age') {
    const ageResult = result as AgeAnalysisResult
    
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6 space-y-6">
        <div className="flex items-center gap-3">
          <Calendar className="w-6 h-6 text-blue-600" />
          <h3 className="text-xl font-semibold text-gray-900">Age Analysis Results</h3>
          {isFreeTier && (
            <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
              Free Analysis
            </span>
          )}
        </div>

        {/* Main Result */}
        <div className="bg-blue-50 rounded-lg p-6 text-center">
          <div className="text-4xl font-bold text-blue-600 mb-2">
            {ageResult.estimatedAge}
          </div>
          <div className="text-lg text-gray-700 mb-1">Estimated Age</div>
          <div className="text-sm text-gray-600">
            Range: {ageResult.ageRange} years
          </div>
        </div>

        {/* Confidence */}
        <div className="flex items-center gap-3">
          <TrendingUp className="w-5 h-5 text-gray-400" />
          <div className="flex-1">
            <div className="flex justify-between items-center mb-1">
              <span className="text-sm font-medium text-gray-700">Confidence</span>
              <span className="text-sm text-gray-600">{ageResult.confidence}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                style={{ width: `${ageResult.confidence}%` }}
              />
            </div>
          </div>
        </div>

        {/* Factors */}
        {ageResult.factors && ageResult.factors.length > 0 && (
          <div>
            <div className="flex items-center gap-2 mb-3">
              <Eye className="w-5 h-5 text-gray-400" />
              <h4 className="font-medium text-gray-900">Key Factors</h4>
            </div>
            <div className="flex flex-wrap gap-2">
              {ageResult.factors.map((factor, index) => (
                <span
                  key={index}
                  className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full"
                >
                  {factor}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Explanation */}
        <div>
          <div className="flex items-center gap-2 mb-3">
            <Lightbulb className="w-5 h-5 text-gray-400" />
            <h4 className="font-medium text-gray-900">Scientific Explanation</h4>
          </div>
          <p className="text-gray-700 leading-relaxed">
            {ageResult.explanation}
          </p>
        </div>
      </div>
    )
  }

  // Emotion Analysis Results
  const emotionResult = result as EmotionAnalysisResult
  
  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 space-y-6">
      <div className="flex items-center gap-3">
        <Heart className="w-6 h-6 text-pink-600" />
        <h3 className="text-xl font-semibold text-gray-900">Emotion Analysis Results</h3>
        {isFreeTier && (
          <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
            Free Analysis
          </span>
        )}
      </div>

      {/* Primary Emotion */}
      <div className="bg-pink-50 rounded-lg p-6 text-center">
        <div className="text-3xl font-bold text-pink-600 mb-2 capitalize">
          {emotionResult.primaryEmotion}
        </div>
        <div className="text-lg text-gray-700">Primary Emotion</div>
      </div>

      {/* Emotion Breakdown */}
      <div>
        <div className="flex items-center gap-2 mb-3">
          <Users className="w-5 h-5 text-gray-400" />
          <h4 className="font-medium text-gray-900">Emotion Breakdown</h4>
        </div>
        <div className="space-y-3">
          {emotionResult.emotions.map((emotion, index) => (
            <div key={index} className="flex items-center gap-3">
              <div className="w-20 text-sm text-gray-600 capitalize">
                {emotion.emotion}
              </div>
              <div className="flex-1">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-pink-600 h-2 rounded-full transition-all duration-500"
                    style={{ width: `${emotion.confidence}%` }}
                  />
                </div>
              </div>
              <div className="w-12 text-sm text-gray-600 text-right">
                {emotion.confidence}%
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Body Language (Advanced only) */}
      {emotionResult.bodyLanguage && (
        <div>
          <div className="flex items-center gap-2 mb-3">
            <Eye className="w-5 h-5 text-gray-400" />
            <h4 className="font-medium text-gray-900">Body Language</h4>
          </div>
          <p className="text-gray-700 leading-relaxed">
            {emotionResult.bodyLanguage}
          </p>
        </div>
      )}

      {/* Context (Advanced only) */}
      {emotionResult.context && (
        <div>
          <div className="flex items-center gap-2 mb-3">
            <Lightbulb className="w-5 h-5 text-gray-400" />
            <h4 className="font-medium text-gray-900">Context Analysis</h4>
          </div>
          <p className="text-gray-700 leading-relaxed">
            {emotionResult.context}
          </p>
        </div>
      )}

      {/* Explanation */}
      <div>
        <div className="flex items-center gap-2 mb-3">
          <Lightbulb className="w-5 h-5 text-gray-400" />
          <h4 className="font-medium text-gray-900">Analysis Explanation</h4>
        </div>
        <p className="text-gray-700 leading-relaxed">
          {emotionResult.explanation}
        </p>
      </div>
    </div>
  )
}
