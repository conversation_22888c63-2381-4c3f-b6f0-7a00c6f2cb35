import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase'
import { checkUserCanAnalyze, deductCredits, getClientIP, AnalysisType, DetailLevel } from '@/lib/credits'
import { analyzeAge, analyzeEmotion } from '@/lib/openai'

export async function POST(request: NextRequest) {
  try {
    const { imageBase64, analysisType, detailLevel } = await request.json()

    if (!imageBase64 || !analysisType || !detailLevel) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Get user from session and client IP
    const supabase = createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    const userId = session?.user?.id || null
    const clientIP = getClientIP(request)

    // Check if user can perform analysis
    const { canAnalyze, reason, isFreeTier } = await checkUserCanAnalyze(
      userId,
      analysisType as AnalysisType,
      detailLevel as DetailLevel,
      clientIP
    )

    if (!canAnalyze) {
      return NextResponse.json(
        { error: reason || 'Cannot perform analysis' },
        { status: 403 }
      )
    }

    // Perform analysis
    let result
    try {
      if (analysisType === 'age') {
        result = await analyzeAge(imageBase64, detailLevel)
      } else if (analysisType === 'emotion') {
        result = await analyzeEmotion(imageBase64, detailLevel)
      } else {
        return NextResponse.json(
          { error: 'Invalid analysis type' },
          { status: 400 }
        )
      }
    } catch (error) {
      console.error('Analysis error:', error)
      return NextResponse.json(
        { error: 'Failed to analyze image' },
        { status: 500 }
      )
    }

    // Deduct credits or track usage
    const { success, error } = await deductCredits(
      userId,
      analysisType as AnalysisType,
      detailLevel as DetailLevel,
      isFreeTier,
      result,
      clientIP
    )

    if (!success) {
      console.error('Failed to deduct credits or track usage:', error)
      // Don't fail the request, but log the error
    }

    return NextResponse.json({
      result,
      isFreeTier,
      analysisType,
      detailLevel
    })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
