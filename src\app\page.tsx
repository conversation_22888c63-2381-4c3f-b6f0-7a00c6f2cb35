'use client'

import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import ImageUpload from '@/components/ImageUpload'
import AnalysisSelector from '@/components/AnalysisSelector'
import AnalysisResults from '@/components/AnalysisResults'
import AuthButton from '@/components/AuthButton'
import UsageLimits from '@/components/UsageLimits'
import { AnalysisType, AgeDetailLevel, EmotionDetailLevel, getCreditCost } from '@/lib/credits'
import { AgeAnalysisResult, EmotionAnalysisResult } from '@/lib/openai'
import { Sparkles, Loader2 } from 'lucide-react'

export default function Home() {
  const { user, userProfile } = useAuth()
  const [selectedImage, setSelectedImage] = useState<File | null>(null)
  const [imageBase64, setImageBase64] = useState<string>('')
  const [analysisType, setAnalysisType] = useState<AnalysisType>('age')
  const [detailLevel, setDetailLevel] = useState<AgeDetailLevel | EmotionDetailLevel>('low')
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [results, setResults] = useState<{
    result: AgeAnalysisResult | EmotionAnalysisResult
    analysisType: AnalysisType
    isFreeTier: boolean
  } | null>(null)
  const [error, setError] = useState<string>('')

  const handleImageSelect = (file: File, base64: string) => {
    setSelectedImage(file)
    setImageBase64(base64)
    setResults(null)
    setError('')
  }

  const handleRemoveImage = () => {
    setSelectedImage(null)
    setImageBase64('')
    setResults(null)
    setError('')
  }

  const handleSelectionChange = (type: AnalysisType, level: AgeDetailLevel | EmotionDetailLevel) => {
    setAnalysisType(type)
    setDetailLevel(level)
    setResults(null)
    setError('')
  }

  const handleAnalyze = async () => {
    if (!imageBase64) {
      setError('Please select an image first')
      return
    }

    setIsAnalyzing(true)
    setError('')
    setResults(null)

    try {
      const response = await fetch('/api/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageBase64,
          analysisType,
          detailLevel,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Analysis failed')
      }

      setResults({
        result: data.result,
        analysisType: data.analysisType,
        isFreeTier: data.isFreeTier,
      })
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsAnalyzing(false)
    }
  }

  const creditCost = getCreditCost(analysisType, detailLevel)
  const userCredits = userProfile?.credits || 0

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Sparkles className="w-8 h-8 text-blue-600" />
            <h1 className="text-2xl font-bold text-gray-900">Guess My Age</h1>
          </div>
          <AuthButton />
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 py-8 space-y-8">
        {/* Hero Section */}
        <div className="text-center space-y-4">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900">
            AI-Powered Photo Analysis
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Upload a photo and let our AI analyze the person's age or emotions with scientific precision.
          </p>
        </div>

        {/* Usage Limits */}
        <div className="max-w-md mx-auto">
          <UsageLimits />
        </div>

        {/* Upload Section */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <ImageUpload
            onImageSelect={handleImageSelect}
            selectedImage={selectedImage}
            onRemoveImage={handleRemoveImage}
          />
        </div>

        {/* Analysis Options */}
        {selectedImage && (
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <AnalysisSelector
              onSelectionChange={handleSelectionChange}
              selectedType={analysisType}
              selectedLevel={detailLevel}
            />
          </div>
        )}

        {/* Analyze Button */}
        {selectedImage && (
          <div className="text-center space-y-4">
            <button
              onClick={handleAnalyze}
              disabled={isAnalyzing}
              className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2 mx-auto"
            >
              {isAnalyzing ? (
                <>
                  <Loader2 className="w-5 h-5 animate-spin" />
                  Analyzing...
                </>
              ) : (
                <>
                  <Sparkles className="w-5 h-5" />
                  Analyze Photo
                </>
              )}
            </button>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-red-700">
            {error}
          </div>
        )}

        {/* Results */}
        {results && (
          <AnalysisResults
            result={results.result}
            analysisType={results.analysisType}
            isFreeTier={results.isFreeTier}
          />
        )}
      </main>
    </div>
  )
}
