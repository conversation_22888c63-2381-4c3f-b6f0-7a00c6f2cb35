import { AgeDetailLevel, EmotionDetailLevel } from './credits'

const OPENAI_API_KEY = process.env.OPENAI_API_KEY!

export interface AgeAnalysisResult {
  estimatedAge: number
  ageRange: string
  confidence: number
  explanation: string
  factors: string[]
}

export interface EmotionAnalysisResult {
  primaryEmotion: string
  emotions: Array<{
    emotion: string
    confidence: number
  }>
  explanation: string
  bodyLanguage?: string
  context?: string
}

const AGE_PROMPTS = {
  low: `Analyze this photo and estimate the person's age. Provide:
1. Estimated age (single number)
2. Age range (e.g., "25-30")
3. Confidence level (0-100)
4. Brief explanation (2-3 sentences)

Focus on basic facial features like skin texture, wrinkles, and overall appearance.`,

  medium: `Analyze this photo and estimate the person's age with moderate detail. Provide:
1. Estimated age (single number)
2. Age range (e.g., "25-30")
3. Confidence level (0-100)
4. Detailed explanation (4-5 sentences)
5. Key factors that influenced your assessment

Consider facial features, skin condition, hair, eyes, and overall facial structure.`,

  high: `Analyze this photo and estimate the person's age with high precision. Provide:
1. Estimated age (single number)
2. Age range (e.g., "25-30")
3. Confidence level (0-100)
4. Comprehensive explanation (6-8 sentences)
5. Detailed list of factors that influenced your assessment
6. Discussion of any challenging aspects or uncertainties

Perform a thorough analysis considering: facial skin texture and elasticity, wrinkle patterns, eye area characteristics, jawline definition, hair condition, neck appearance, hand visibility (if present), overall facial proportions, and any other age-related indicators.`
}

const EMOTION_PROMPTS = {
  simple: `Analyze the emotions shown in this photo. Provide:
1. Primary emotion
2. List of detected emotions with confidence levels
3. Brief explanation (2-3 sentences)

Focus on basic facial expressions and obvious emotional indicators.`,

  advanced: `Perform an advanced emotional analysis of this photo. Provide:
1. Primary emotion
2. Detailed list of detected emotions with confidence levels
3. Comprehensive explanation (4-6 sentences)
4. Body language analysis (if visible)
5. Contextual emotional indicators

Consider micro-expressions, eye contact, posture, hand gestures, environmental context, and subtle emotional cues that might not be immediately obvious.`
}

export async function analyzeAge(
  imageBase64: string,
  detailLevel: AgeDetailLevel
): Promise<AgeAnalysisResult> {
  try {
    const response = await fetch('https://api.openai.com/v1/responses', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4.1-mini',
        input: [{
          role: 'user',
          content: [
            { type: 'input_text', text: AGE_PROMPTS[detailLevel] },
            {
              type: 'input_image',
              image_url: imageBase64,
              detail: detailLevel === 'low' ? 'low' : 'high'
            }
          ]
        }]
      })
    })

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`)
    }

    const data = await response.json()
    console.log('OpenAI Response:', JSON.stringify(data, null, 2))

    // Extract content from the new response structure
    let content = ''
    if (data.output && Array.isArray(data.output) && data.output.length > 0) {
      const message = data.output[0]
      if (message.content && Array.isArray(message.content) && message.content.length > 0) {
        const textContent = message.content.find((c: any) => c.type === 'output_text')
        if (textContent && textContent.text) {
          content = textContent.text
        }
      }
    }

    console.log('Extracted content:', content)

    if (!content) {
      throw new Error('No content received from OpenAI API')
    }

    // Parse the response to extract structured data
    const result = parseAgeAnalysis(content)
    return result
  } catch (error) {
    console.error('Error analyzing age:', error)
    throw new Error('Failed to analyze age')
  }
}

export async function analyzeEmotion(
  imageBase64: string,
  detailLevel: EmotionDetailLevel
): Promise<EmotionAnalysisResult> {
  try {
    const response = await fetch('https://api.openai.com/v1/responses', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4.1-mini',
        input: [{
          role: 'user',
          content: [
            { type: 'input_text', text: EMOTION_PROMPTS[detailLevel] },
            {
              type: 'input_image',
              image_url: imageBase64,
              detail: detailLevel === 'simple' ? 'low' : 'high'
            }
          ]
        }]
      })
    })

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`)
    }

    const data = await response.json()
    console.log('OpenAI Emotion Response:', JSON.stringify(data, null, 2))

    // Extract content from the new response structure
    let content = ''
    if (data.output && Array.isArray(data.output) && data.output.length > 0) {
      const message = data.output[0]
      if (message.content && Array.isArray(message.content) && message.content.length > 0) {
        const textContent = message.content.find((c: any) => c.type === 'output_text')
        if (textContent && textContent.text) {
          content = textContent.text
        }
      }
    }

    console.log('Extracted emotion content:', content)

    if (!content) {
      throw new Error('No content received from OpenAI API')
    }

    // Parse the response to extract structured data
    const result = parseEmotionAnalysis(content, detailLevel)
    return result
  } catch (error) {
    console.error('Error analyzing emotion:', error)
    throw new Error('Failed to analyze emotion')
  }
}

function parseAgeAnalysis(content: string): AgeAnalysisResult {
  // Ensure content is a string
  if (!content || typeof content !== 'string') {
    console.warn('Invalid content for age analysis:', content)
    return {
      estimatedAge: 25,
      ageRange: '20-30',
      confidence: 50,
      explanation: 'Unable to analyze the image. Please try again.',
      factors: ['Analysis failed']
    }
  }

  // Simple parsing - in a real app, you might want more sophisticated parsing
  const ageMatch = content.match(/(?:age|estimated)[:\s]*(\d+)/i)
  const rangeMatch = content.match(/(?:range|between)[:\s]*(\d+[-–]\d+)/i)
  const confidenceMatch = content.match(/confidence[:\s]*(\d+)/i)

  return {
    estimatedAge: ageMatch ? parseInt(ageMatch[1]) : 25,
    ageRange: rangeMatch ? rangeMatch[1] : '20-30',
    confidence: confidenceMatch ? parseInt(confidenceMatch[1]) : 75,
    explanation: content || 'Analysis completed but no detailed explanation available.',
    factors: extractFactors(content)
  }
}

function parseEmotionAnalysis(content: string, detailLevel: EmotionDetailLevel): EmotionAnalysisResult {
  // Ensure content is a string
  if (!content || typeof content !== 'string') {
    console.warn('Invalid content for emotion analysis:', content)
    return {
      primaryEmotion: 'neutral',
      emotions: [
        { emotion: 'neutral', confidence: 100 }
      ],
      explanation: 'Unable to analyze emotions. Please try again.'
    }
  }

  // Extract primary emotion
  const emotionMatch = content.match(/(?:primary|main|dominant)\s+emotion[:\s]*(\w+)/i)
  const primaryEmotion = emotionMatch ? emotionMatch[1] : 'neutral'

  // Extract emotions list (simplified)
  const emotions = [
    { emotion: primaryEmotion, confidence: 85 },
    { emotion: 'neutral', confidence: 15 }
  ]

  const result: EmotionAnalysisResult = {
    primaryEmotion,
    emotions,
    explanation: content || 'Emotion analysis completed but no detailed explanation available.'
  }

  if (detailLevel === 'advanced') {
    result.bodyLanguage = 'Body language analysis included in explanation'
    result.context = 'Contextual analysis included in explanation'
  }

  return result
}

function extractFactors(content: string): string[] {
  // Ensure content is a string
  if (!content || typeof content !== 'string') {
    return ['Analysis factors unavailable']
  }

  // Simple factor extraction - could be improved with better parsing
  const factors = []
  const lowerContent = content.toLowerCase()

  if (lowerContent.includes('wrinkle')) factors.push('Facial wrinkles')
  if (lowerContent.includes('skin')) factors.push('Skin texture')
  if (lowerContent.includes('hair')) factors.push('Hair condition')
  if (lowerContent.includes('eye')) factors.push('Eye area')
  if (lowerContent.includes('face')) factors.push('Facial structure')
  if (lowerContent.includes('jaw')) factors.push('Jawline definition')

  return factors.length > 0 ? factors : ['General facial features']
}
