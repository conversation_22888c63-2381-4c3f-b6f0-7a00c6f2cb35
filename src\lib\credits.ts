import { supabase, createServerClient } from './supabase'
import { NextRequest } from 'next/server'

export const CREDIT_COSTS = {
  age: {
    low: 1,
    medium: 2,
    high: 3,
  },
  emotion: {
    simple: 1,
    advanced: 2,
  }
} as const

export const DAILY_FREE_LIMITS = {
  anonymous: 1,
  registered: 3,
} as const

export type AnalysisType = 'age' | 'emotion'
export type AgeDetailLevel = 'low' | 'medium' | 'high'
export type EmotionDetailLevel = 'simple' | 'advanced'
export type DetailLevel = AgeDetailLevel | EmotionDetailLevel

export function getCreditCost(analysisType: AnalysisType, detailLevel: DetailLevel): number {
  if (analysisType === 'age') {
    return CREDIT_COSTS.age[detailLevel as AgeDetailLevel]
  } else {
    return CREDIT_COSTS.emotion[detailLevel as EmotionDetailLevel]
  }
}

export function getClientIP(request: NextRequest): string {
  // Try to get IP from various headers
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const cfConnectingIP = request.headers.get('cf-connecting-ip')

  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  if (realIP) {
    return realIP
  }
  if (cfConnectingIP) {
    return cfConnectingIP
  }

  // Fallback to localhost
  return '127.0.0.1'
}

export async function checkUserCanAnalyze(
  userId: string | null,
  analysisType: AnalysisType,
  detailLevel: DetailLevel,
  clientIP?: string
): Promise<{ canAnalyze: boolean; reason?: string; isFreeTier: boolean }> {
  const creditCost = getCreditCost(analysisType, detailLevel)

  // Anonymous user
  if (!userId) {
    if (!clientIP) {
      return {
        canAnalyze: false,
        reason: 'Unable to identify user',
        isFreeTier: false,
      }
    }

    // For free tier, only allow basic analysis (cost = 1)
    if (creditCost > 1) {
      return {
        canAnalyze: false,
        reason: 'Advanced analysis requires registration and credits',
        isFreeTier: false,
      }
    }

    // Check anonymous user daily limit
    try {
      const today = new Date().toISOString().split('T')[0]
      const supabaseServer = createServerClient()

      const { data: usage, error } = await supabaseServer
        .from('anonymous_usage')
        .select('daily_uses')
        .eq('ip_address', clientIP)
        .eq('usage_date', today)
        .single()

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows found
        console.error('Error checking anonymous usage:', error)
        return {
          canAnalyze: false,
          reason: 'Error checking usage limits',
          isFreeTier: false,
        }
      }

      const dailyUses = usage?.daily_uses || 0

      if (dailyUses >= DAILY_FREE_LIMITS.anonymous) {
        return {
          canAnalyze: false,
          reason: 'Daily free limit reached. Please register for more uses.',
          isFreeTier: false,
        }
      }

      return {
        canAnalyze: true,
        isFreeTier: true,
      }
    } catch (error) {
      console.error('Error checking anonymous usage:', error)
      return {
        canAnalyze: false,
        reason: 'Error checking usage limits',
        isFreeTier: false,
      }
    }
  }

  // Registered user
  try {
    const { data: user, error } = await supabase
      .from('users')
      .select('credits, daily_free_uses, last_free_use_date')
      .eq('id', userId)
      .single()

    if (error || !user) {
      return {
        canAnalyze: false,
        reason: 'User not found',
        isFreeTier: false,
      }
    }

    const today = new Date().toISOString().split('T')[0]
    const lastFreeUseDate = user.last_free_use_date

    // Reset daily free uses if it's a new day
    let dailyFreeUses = user.daily_free_uses
    if (lastFreeUseDate !== today) {
      dailyFreeUses = 0
    }

    // Check if user can use free tier
    if (dailyFreeUses < DAILY_FREE_LIMITS.registered && creditCost === 1) {
      return {
        canAnalyze: true,
        isFreeTier: true,
      }
    }

    // Check if user has enough credits
    if (user.credits >= creditCost) {
      return {
        canAnalyze: true,
        isFreeTier: false,
      }
    }

    return {
      canAnalyze: false,
      reason: `Not enough credits. Need ${creditCost}, have ${user.credits}`,
      isFreeTier: false,
    }
  } catch (error) {
    console.error('Error checking user credits:', error)
    return {
      canAnalyze: false,
      reason: 'Error checking credits',
      isFreeTier: false,
    }
  }
}

export async function trackAnonymousUsage(
  clientIP: string,
  _analysisType: AnalysisType,
  _detailLevel: DetailLevel,
  _resultData: any
): Promise<{ success: boolean; error?: string }> {
  try {
    const today = new Date().toISOString().split('T')[0]
    const supabaseServer = createServerClient()

    // Insert or update anonymous usage
    const { error: rpcError } = await supabaseServer
      .rpc('track_anonymous_usage', {
        ip_addr: clientIP,
        usage_date_param: today
      })

    if (rpcError) {
      console.error('Error tracking anonymous usage:', rpcError)
      return { success: false, error: 'Failed to track usage' }
    }

    return { success: true }
  } catch (error) {
    console.error('Error in trackAnonymousUsage:', error)
    return { success: false, error: 'Unexpected error' }
  }
}

export async function deductCredits(
  userId: string | null,
  analysisType: AnalysisType,
  detailLevel: DetailLevel,
  isFreeTier: boolean,
  resultData: any,
  clientIP?: string
): Promise<{ success: boolean; error?: string }> {
  const creditCost = getCreditCost(analysisType, detailLevel)

  try {
    const supabaseServer = createServerClient()

    // Handle anonymous users
    if (!userId) {
      if (!clientIP) {
        return { success: false, error: 'Missing client IP for anonymous user' }
      }
      return await trackAnonymousUsage(clientIP, analysisType, detailLevel, resultData)
    }

    // Handle registered users
    if (isFreeTier) {
      // Update daily free uses
      const today = new Date().toISOString().split('T')[0]

      const { error: updateError } = await supabaseServer
        .rpc('increment_daily_free_uses', {
          user_id: userId,
          new_date: today
        })

      if (updateError) {
        console.error('Error updating daily free uses:', updateError)
        return { success: false, error: 'Failed to update usage' }
      }
    } else {
      // Deduct credits
      const { error: updateError } = await supabaseServer
        .rpc('deduct_user_credits', {
          user_id: userId,
          credits_to_deduct: creditCost
        })

      if (updateError) {
        console.error('Error deducting credits:', updateError)
        return { success: false, error: 'Failed to deduct credits' }
      }
    }

    // Log the usage (only for registered users)
    if (userId) {
      const { error: logError } = await supabaseServer
        .from('usage_logs')
        .insert({
          user_id: userId,
          analysis_type: analysisType,
          detail_level: detailLevel,
          credits_used: isFreeTier ? 0 : creditCost,
          is_free_use: isFreeTier,
          result_data: resultData,
        })

      if (logError) {
        console.error('Error logging usage:', logError)
        // Don't fail the operation if logging fails
      }
    }

    return { success: true }
  } catch (error) {
    console.error('Error in deductCredits:', error)
    return { success: false, error: 'Unexpected error' }
  }
}

export async function getUserLimits(
  userId: string | null,
  clientIP?: string
): Promise<{
  isAnonymous: boolean
  dailyFreeUsed: number
  dailyFreeLimit: number
  credits: number
  canUseAdvanced: boolean
}> {
  if (!userId) {
    // Anonymous user
    if (!clientIP) {
      return {
        isAnonymous: true,
        dailyFreeUsed: 1, // Assume limit reached if no IP
        dailyFreeLimit: DAILY_FREE_LIMITS.anonymous,
        credits: 0,
        canUseAdvanced: false,
      }
    }

    try {
      const today = new Date().toISOString().split('T')[0]
      const supabaseServer = createServerClient()

      const { data: usage } = await supabaseServer
        .from('anonymous_usage')
        .select('daily_uses')
        .eq('ip_address', clientIP)
        .eq('usage_date', today)
        .single()

      const dailyUses = usage?.daily_uses || 0

      return {
        isAnonymous: true,
        dailyFreeUsed: dailyUses,
        dailyFreeLimit: DAILY_FREE_LIMITS.anonymous,
        credits: 0,
        canUseAdvanced: false,
      }
    } catch (error) {
      return {
        isAnonymous: true,
        dailyFreeUsed: 0,
        dailyFreeLimit: DAILY_FREE_LIMITS.anonymous,
        credits: 0,
        canUseAdvanced: false,
      }
    }
  }

  // Registered user
  try {
    const supabaseServer = createServerClient()
    const { data: user, error } = await supabaseServer
      .from('users')
      .select('credits, daily_free_uses, last_free_use_date')
      .eq('id', userId)
      .single()

    if (error || !user) {
      return {
        isAnonymous: false,
        dailyFreeUsed: DAILY_FREE_LIMITS.registered,
        dailyFreeLimit: DAILY_FREE_LIMITS.registered,
        credits: 0,
        canUseAdvanced: false,
      }
    }

    const today = new Date().toISOString().split('T')[0]
    const lastFreeUseDate = user.last_free_use_date

    // Reset daily free uses if it's a new day
    let dailyFreeUses = user.daily_free_uses
    if (lastFreeUseDate !== today) {
      dailyFreeUses = 0
    }

    return {
      isAnonymous: false,
      dailyFreeUsed: dailyFreeUses,
      dailyFreeLimit: DAILY_FREE_LIMITS.registered,
      credits: user.credits,
      canUseAdvanced: user.credits > 0,
    }
  } catch (error) {
    return {
      isAnonymous: false,
      dailyFreeUsed: DAILY_FREE_LIMITS.registered,
      dailyFreeLimit: DAILY_FREE_LIMITS.registered,
      credits: 0,
      canUseAdvanced: false,
    }
  }
}

export async function addCredits(
  userId: string,
  credits: number,
  _transactionId?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabaseServer = createServerClient()

    const { error } = await supabaseServer
      .rpc('add_user_credits', {
        user_id: userId,
        credits_to_add: credits
      })

    if (error) {
      console.error('Error adding credits:', error)
      return { success: false, error: 'Failed to add credits' }
    }

    return { success: true }
  } catch (error) {
    console.error('Error in addCredits:', error)
    return { success: false, error: 'Unexpected error' }
  }
}
