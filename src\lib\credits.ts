import { supabase, createServerClient } from './supabase'
import { NextRequest } from 'next/server'

export const CREDIT_COSTS = {
  age: {
    low: 1,
    medium: 2,
    high: 3,
  },
  emotion: {
    simple: 1,
    advanced: 2,
  }
} as const

export const DAILY_FREE_LIMITS = {
  anonymous: 1,
  registered: 3,
} as const

export type AnalysisType = 'age' | 'emotion'
export type AgeDetailLevel = 'low' | 'medium' | 'high'
export type EmotionDetailLevel = 'simple' | 'advanced'
export type DetailLevel = AgeDetailLevel | EmotionDetailLevel

export function getCreditCost(analysisType: AnalysisType, detailLevel: DetailLevel): number {
  if (analysisType === 'age') {
    return CREDIT_COSTS.age[detailLevel as AgeDetailLevel]
  } else {
    return CREDIT_COSTS.emotion[detailLevel as EmotionDetailLevel]
  }
}

export function getClientIP(request: NextRequest): string {
  // Try to get IP from various headers
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const cfConnectingIP = request.headers.get('cf-connecting-ip')

  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  if (realIP) {
    return realIP
  }
  if (cfConnectingIP) {
    return cfConnectingIP
  }

  // Fallback to connection remote address
  return request.ip || '127.0.0.1'
}

export async function checkUserCanAnalyze(
  userId: string | null,
  analysisType: AnalysisType,
  detailLevel: DetailLevel,
  clientIP?: string
): Promise<{ canAnalyze: boolean; reason?: string; isFreeTier: boolean }> {
  const creditCost = getCreditCost(analysisType, detailLevel)

  // Anonymous user
  if (!userId) {
    if (!clientIP) {
      return {
        canAnalyze: false,
        reason: 'Unable to identify user',
        isFreeTier: false,
      }
    }

    // For free tier, only allow basic analysis (cost = 1)
    if (creditCost > 1) {
      return {
        canAnalyze: false,
        reason: 'Advanced analysis requires registration and credits',
        isFreeTier: false,
      }
    }

    // Check anonymous user daily limit
    try {
      const today = new Date().toISOString().split('T')[0]
      const supabaseServer = createServerClient()

      const { data: usage, error } = await supabaseServer
        .from('anonymous_usage')
        .select('daily_uses')
        .eq('ip_address', clientIP)
        .eq('usage_date', today)
        .single()

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows found
        console.error('Error checking anonymous usage:', error)
        return {
          canAnalyze: false,
          reason: 'Error checking usage limits',
          isFreeTier: false,
        }
      }

      const dailyUses = usage?.daily_uses || 0

      if (dailyUses >= DAILY_FREE_LIMITS.anonymous) {
        return {
          canAnalyze: false,
          reason: 'Daily free limit reached. Please register for more uses.',
          isFreeTier: false,
        }
      }

      return {
        canAnalyze: true,
        isFreeTier: true,
      }
    } catch (error) {
      console.error('Error checking anonymous usage:', error)
      return {
        canAnalyze: false,
        reason: 'Error checking usage limits',
        isFreeTier: false,
      }
    }
  }

  // Registered user
  try {
    const { data: user, error } = await supabase
      .from('users')
      .select('credits, daily_free_uses, last_free_use_date')
      .eq('id', userId)
      .single()

    if (error || !user) {
      return {
        canAnalyze: false,
        reason: 'User not found',
        isFreeTier: false,
      }
    }

    const today = new Date().toISOString().split('T')[0]
    const lastFreeUseDate = user.last_free_use_date

    // Reset daily free uses if it's a new day
    let dailyFreeUses = user.daily_free_uses
    if (lastFreeUseDate !== today) {
      dailyFreeUses = 0
    }

    // Check if user can use free tier
    if (dailyFreeUses < DAILY_FREE_LIMITS.registered && creditCost === 1) {
      return {
        canAnalyze: true,
        isFreeTier: true,
      }
    }

    // Check if user has enough credits
    if (user.credits >= creditCost) {
      return {
        canAnalyze: true,
        isFreeTier: false,
      }
    }

    return {
      canAnalyze: false,
      reason: `Not enough credits. Need ${creditCost}, have ${user.credits}`,
      isFreeTier: false,
    }
  } catch (error) {
    console.error('Error checking user credits:', error)
    return {
      canAnalyze: false,
      reason: 'Error checking credits',
      isFreeTier: false,
    }
  }
}

export async function deductCredits(
  userId: string,
  analysisType: AnalysisType,
  detailLevel: DetailLevel,
  isFreeTier: boolean,
  resultData: any
): Promise<{ success: boolean; error?: string }> {
  const creditCost = getCreditCost(analysisType, detailLevel)

  try {
    const supabaseServer = createServerClient()

    if (isFreeTier) {
      // Update daily free uses
      const today = new Date().toISOString().split('T')[0]
      
      const { error: updateError } = await supabaseServer
        .from('users')
        .update({
          daily_free_uses: supabaseServer.raw('daily_free_uses + 1'),
          last_free_use_date: today,
        })
        .eq('id', userId)

      if (updateError) {
        console.error('Error updating daily free uses:', updateError)
        return { success: false, error: 'Failed to update usage' }
      }
    } else {
      // Deduct credits
      const { error: updateError } = await supabaseServer
        .from('users')
        .update({
          credits: supabaseServer.raw(`credits - ${creditCost}`),
        })
        .eq('id', userId)

      if (updateError) {
        console.error('Error deducting credits:', updateError)
        return { success: false, error: 'Failed to deduct credits' }
      }
    }

    // Log the usage
    const { error: logError } = await supabaseServer
      .from('usage_logs')
      .insert({
        user_id: userId,
        analysis_type: analysisType,
        detail_level: detailLevel,
        credits_used: isFreeTier ? 0 : creditCost,
        is_free_use: isFreeTier,
        result_data: resultData,
      })

    if (logError) {
      console.error('Error logging usage:', logError)
      // Don't fail the operation if logging fails
    }

    return { success: true }
  } catch (error) {
    console.error('Error in deductCredits:', error)
    return { success: false, error: 'Unexpected error' }
  }
}

export async function addCredits(
  userId: string,
  credits: number,
  transactionId?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabaseServer = createServerClient()

    const { error } = await supabaseServer
      .from('users')
      .update({
        credits: supabaseServer.raw(`credits + ${credits}`),
      })
      .eq('id', userId)

    if (error) {
      console.error('Error adding credits:', error)
      return { success: false, error: 'Failed to add credits' }
    }

    return { success: true }
  } catch (error) {
    console.error('Error in addCredits:', error)
    return { success: false, error: 'Unexpected error' }
  }
}
