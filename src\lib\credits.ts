import { supabase, createServerClient } from './supabase'

export const CREDIT_COSTS = {
  age: {
    low: 1,
    medium: 2,
    high: 3,
  },
  emotion: {
    simple: 1,
    advanced: 2,
  }
} as const

export const DAILY_FREE_LIMITS = {
  anonymous: 1,
  registered: 3,
} as const

export type AnalysisType = 'age' | 'emotion'
export type AgeDetailLevel = 'low' | 'medium' | 'high'
export type EmotionDetailLevel = 'simple' | 'advanced'
export type DetailLevel = AgeDetailLevel | EmotionDetailLevel

export function getCreditCost(analysisType: AnalysisType, detailLevel: DetailLevel): number {
  if (analysisType === 'age') {
    return CREDIT_COSTS.age[detailLevel as AgeDetailLevel]
  } else {
    return CREDIT_COSTS.emotion[detailLevel as EmotionDetailLevel]
  }
}

export async function checkUserCanAnalyze(
  userId: string | null,
  analysisType: AnalysisType,
  detailLevel: DetailLevel
): Promise<{ canAnalyze: boolean; reason?: string; isFreeTier: boolean }> {
  const creditCost = getCreditCost(analysisType, detailLevel)

  // Anonymous user
  if (!userId) {
    // Check if anonymous user has used their daily free analysis
    const today = new Date().toISOString().split('T')[0]
    const anonymousUsageKey = `anonymous_usage_${today}`
    
    // In a real app, you'd store this in localStorage or a session
    // For now, we'll allow 1 free use per session for anonymous users
    return {
      canAnalyze: true,
      isFreeTier: true,
    }
  }

  // Registered user
  try {
    const { data: user, error } = await supabase
      .from('users')
      .select('credits, daily_free_uses, last_free_use_date')
      .eq('id', userId)
      .single()

    if (error || !user) {
      return {
        canAnalyze: false,
        reason: 'User not found',
        isFreeTier: false,
      }
    }

    const today = new Date().toISOString().split('T')[0]
    const lastFreeUseDate = user.last_free_use_date

    // Reset daily free uses if it's a new day
    let dailyFreeUses = user.daily_free_uses
    if (lastFreeUseDate !== today) {
      dailyFreeUses = 0
    }

    // Check if user can use free tier
    if (dailyFreeUses < DAILY_FREE_LIMITS.registered && creditCost === 1) {
      return {
        canAnalyze: true,
        isFreeTier: true,
      }
    }

    // Check if user has enough credits
    if (user.credits >= creditCost) {
      return {
        canAnalyze: true,
        isFreeTier: false,
      }
    }

    return {
      canAnalyze: false,
      reason: `Not enough credits. Need ${creditCost}, have ${user.credits}`,
      isFreeTier: false,
    }
  } catch (error) {
    console.error('Error checking user credits:', error)
    return {
      canAnalyze: false,
      reason: 'Error checking credits',
      isFreeTier: false,
    }
  }
}

export async function deductCredits(
  userId: string,
  analysisType: AnalysisType,
  detailLevel: DetailLevel,
  isFreeTier: boolean,
  resultData: any
): Promise<{ success: boolean; error?: string }> {
  const creditCost = getCreditCost(analysisType, detailLevel)

  try {
    const supabaseServer = createServerClient()

    if (isFreeTier) {
      // Update daily free uses
      const today = new Date().toISOString().split('T')[0]
      
      const { error: updateError } = await supabaseServer
        .from('users')
        .update({
          daily_free_uses: supabaseServer.raw('daily_free_uses + 1'),
          last_free_use_date: today,
        })
        .eq('id', userId)

      if (updateError) {
        console.error('Error updating daily free uses:', updateError)
        return { success: false, error: 'Failed to update usage' }
      }
    } else {
      // Deduct credits
      const { error: updateError } = await supabaseServer
        .from('users')
        .update({
          credits: supabaseServer.raw(`credits - ${creditCost}`),
        })
        .eq('id', userId)

      if (updateError) {
        console.error('Error deducting credits:', updateError)
        return { success: false, error: 'Failed to deduct credits' }
      }
    }

    // Log the usage
    const { error: logError } = await supabaseServer
      .from('usage_logs')
      .insert({
        user_id: userId,
        analysis_type: analysisType,
        detail_level: detailLevel,
        credits_used: isFreeTier ? 0 : creditCost,
        is_free_use: isFreeTier,
        result_data: resultData,
      })

    if (logError) {
      console.error('Error logging usage:', logError)
      // Don't fail the operation if logging fails
    }

    return { success: true }
  } catch (error) {
    console.error('Error in deductCredits:', error)
    return { success: false, error: 'Unexpected error' }
  }
}

export async function addCredits(
  userId: string,
  credits: number,
  transactionId?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabaseServer = createServerClient()

    const { error } = await supabaseServer
      .from('users')
      .update({
        credits: supabaseServer.raw(`credits + ${credits}`),
      })
      .eq('id', userId)

    if (error) {
      console.error('Error adding credits:', error)
      return { success: false, error: 'Failed to add credits' }
    }

    return { success: true }
  } catch (error) {
    console.error('Error in addCredits:', error)
    return { success: false, error: 'Unexpected error' }
  }
}
