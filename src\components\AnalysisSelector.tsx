'use client'

import { useState } from 'react'
import { Calendar, Heart, Zap, Star, Crown } from 'lucide-react'
import { AnalysisType, AgeDetailLevel, EmotionDetailLevel, getCreditCost } from '@/lib/credits'

interface AnalysisSelectorProps {
  onSelectionChange: (type: AnalysisType, level: AgeDetailLevel | EmotionDetailLevel) => void
  selectedType: AnalysisType
  selectedLevel: AgeDetailLevel | EmotionDetailLevel
}

export default function AnalysisSelector({ onSelectionChange, selectedType, selectedLevel }: AnalysisSelectorProps) {
  const handleTypeChange = (type: AnalysisType) => {
    const defaultLevel = type === 'age' ? 'low' : 'simple'
    onSelectionChange(type, defaultLevel)
  }

  const handleLevelChange = (level: AgeDetailLevel | EmotionDetailLevel) => {
    onSelectionChange(selectedType, level)
  }

  return (
    <div className="space-y-6">
      {/* Analysis Type Selection */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-3">Choose Analysis Type</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <button
            onClick={() => handleTypeChange('age')}
            className={`
              p-4 rounded-lg border-2 transition-all text-left
              ${selectedType === 'age'
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
              }
            `}
          >
            <div className="flex items-center gap-3">
              <Calendar className={`w-6 h-6 ${selectedType === 'age' ? 'text-blue-600' : 'text-gray-400'}`} />
              <div>
                <h4 className="font-medium text-gray-900">Age Estimation</h4>
                <p className="text-sm text-gray-600">Estimate the person's age</p>
              </div>
            </div>
          </button>

          <button
            onClick={() => handleTypeChange('emotion')}
            className={`
              p-4 rounded-lg border-2 transition-all text-left
              ${selectedType === 'emotion'
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
              }
            `}
          >
            <div className="flex items-center gap-3">
              <Heart className={`w-6 h-6 ${selectedType === 'emotion' ? 'text-blue-600' : 'text-gray-400'}`} />
              <div>
                <h4 className="font-medium text-gray-900">Emotion Analysis</h4>
                <p className="text-sm text-gray-600">Detect emotions and feelings</p>
              </div>
            </div>
          </button>
        </div>
      </div>

      {/* Detail Level Selection */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-3">
          {selectedType === 'age' ? 'Analysis Detail Level' : 'Analysis Mode'}
        </h3>
        
        {selectedType === 'age' ? (
          <div className="space-y-3">
            {(['low', 'medium', 'high'] as AgeDetailLevel[]).map((level) => {
              const cost = getCreditCost('age', level)
              const isSelected = selectedLevel === level
              
              return (
                <button
                  key={level}
                  onClick={() => handleLevelChange(level)}
                  className={`
                    w-full p-4 rounded-lg border-2 transition-all text-left
                    ${isSelected
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                    }
                  `}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {level === 'low' && <Zap className={`w-5 h-5 ${isSelected ? 'text-blue-600' : 'text-gray-400'}`} />}
                      {level === 'medium' && <Star className={`w-5 h-5 ${isSelected ? 'text-blue-600' : 'text-gray-400'}`} />}
                      {level === 'high' && <Crown className={`w-5 h-5 ${isSelected ? 'text-blue-600' : 'text-gray-400'}`} />}
                      <div>
                        <h4 className="font-medium text-gray-900 capitalize">{level} Detail</h4>
                        <p className="text-sm text-gray-600">
                          {level === 'low' && 'Basic age estimation'}
                          {level === 'medium' && 'Detailed analysis with factors'}
                          {level === 'high' && 'Comprehensive analysis with full explanation'}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-900">{cost} credit{cost > 1 ? 's' : ''}</div>
                    </div>
                  </div>
                </button>
              )
            })}
          </div>
        ) : (
          <div className="space-y-3">
            {(['simple', 'advanced'] as EmotionDetailLevel[]).map((level) => {
              const cost = getCreditCost('emotion', level)
              const isSelected = selectedLevel === level
              
              return (
                <button
                  key={level}
                  onClick={() => handleLevelChange(level)}
                  className={`
                    w-full p-4 rounded-lg border-2 transition-all text-left
                    ${isSelected
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                    }
                  `}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {level === 'simple' && <Zap className={`w-5 h-5 ${isSelected ? 'text-blue-600' : 'text-gray-400'}`} />}
                      {level === 'advanced' && <Star className={`w-5 h-5 ${isSelected ? 'text-blue-600' : 'text-gray-400'}`} />}
                      <div>
                        <h4 className="font-medium text-gray-900 capitalize">{level} Analysis</h4>
                        <p className="text-sm text-gray-600">
                          {level === 'simple' && 'Basic emotion detection'}
                          {level === 'advanced' && 'Advanced analysis with body language and context'}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-900">{cost} credit{cost > 1 ? 's' : ''}</div>
                    </div>
                  </div>
                </button>
              )
            })}
          </div>
        )}
      </div>
    </div>
  )
}
