import { createClient } from '@supabase/supabase-js'
import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Client-side Supabase client
export const supabase = createBrowserClient(supabaseUrl, supabaseAnonKey)

// Server-side Supabase client (for API routes)
export const createServerClient = () => {
  return createClient(
    supabaseUrl,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  )
}

// Database types
export interface User {
  id: string
  email: string | null
  credits: number
  daily_free_uses: number
  last_free_use_date: string
  created_at: string
  updated_at: string
}

export interface Transaction {
  id: string
  user_id: string
  stripe_payment_intent_id: string | null
  amount_cents: number
  credits_purchased: number
  status: 'pending' | 'completed' | 'failed'
  created_at: string
}

export interface UsageLog {
  id: string
  user_id: string
  analysis_type: 'age' | 'emotion'
  detail_level: 'low' | 'medium' | 'high' | 'simple' | 'advanced'
  credits_used: number
  is_free_use: boolean
  result_data: any
  created_at: string
}
