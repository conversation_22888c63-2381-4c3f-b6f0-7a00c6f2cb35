import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase'
import { getUserLimits, getClientIP } from '@/lib/credits'

export async function GET(request: NextRequest) {
  try {
    // Get user from session and client IP
    const supabase = createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    const userId = session?.user?.id || null
    const clientIP = getClientIP(request)

    // Get user limits
    const limits = await getUserLimits(userId, clientIP)

    return NextResponse.json(limits)
  } catch (error) {
    console.error('Error getting user limits:', error)
    return NextResponse.json(
      { error: 'Failed to get user limits' },
      { status: 500 }
    )
  }
}
