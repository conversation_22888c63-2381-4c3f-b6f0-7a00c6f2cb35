'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { AlertCircle, Crown, Zap } from 'lucide-react'
import AuthModal from './AuthModal'

interface UserLimits {
  isAnonymous: boolean
  dailyFreeUsed: number
  dailyFreeLimit: number
  credits: number
  canUseAdvanced: boolean
}

export default function UsageLimits() {
  const { user } = useAuth()
  const [limits, setLimits] = useState<UserLimits | null>(null)
  const [loading, setLoading] = useState(true)
  const [showAuthModal, setShowAuthModal] = useState(false)

  useEffect(() => {
    fetchLimits()
  }, [user])

  const fetchLimits = async () => {
    try {
      const response = await fetch('/api/user-limits')
      if (response.ok) {
        const data = await response.json()
        setLimits(data)
      }
    } catch (error) {
      console.error('Error fetching limits:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="bg-gray-50 rounded-lg p-4 animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-3 bg-gray-200 rounded w-1/2"></div>
      </div>
    )
  }

  if (!limits) {
    return null
  }

  const remainingFree = Math.max(0, limits.dailyFreeLimit - limits.dailyFreeUsed)
  const isLimitReached = remainingFree === 0

  return (
    <div className="space-y-4">
      {/* Usage Status */}
      <div className={`rounded-lg p-4 ${
        isLimitReached ? 'bg-red-50 border border-red-200' : 'bg-blue-50 border border-blue-200'
      }`}>
        <div className="flex items-center gap-2 mb-2">
          {isLimitReached ? (
            <AlertCircle className="w-5 h-5 text-red-500" />
          ) : (
            <Zap className="w-5 h-5 text-blue-500" />
          )}
          <h3 className={`font-medium ${
            isLimitReached ? 'text-red-800' : 'text-blue-800'
          }`}>
            {limits.isAnonymous ? 'Guest User' : 'Registered User'}
          </h3>
        </div>
        
        <div className="space-y-2">
          <div className={`text-sm ${
            isLimitReached ? 'text-red-700' : 'text-blue-700'
          }`}>
            Daily free analyses: {limits.dailyFreeUsed} / {limits.dailyFreeLimit}
          </div>
          
          {!limits.isAnonymous && (
            <div className="text-sm text-blue-700">
              Credits available: {limits.credits}
            </div>
          )}
          
          {remainingFree > 0 && (
            <div className="text-sm text-green-600 font-medium">
              {remainingFree} free {remainingFree === 1 ? 'analysis' : 'analyses'} remaining today
            </div>
          )}
        </div>
      </div>

      {/* Upgrade Prompts */}
      {limits.isAnonymous && (
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <Crown className="w-5 h-5 text-purple-500" />
            <h3 className="font-medium text-purple-800">Unlock More Features</h3>
          </div>
          <p className="text-sm text-purple-700 mb-3">
            Register for free to get 3 daily analyses instead of 1, plus the ability to purchase credits for advanced features.
          </p>
          <button
            onClick={() => setShowAuthModal(true)}
            className="bg-purple-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-purple-700 transition-colors"
          >
            Sign Up Free
          </button>
        </div>
      )}

      {!limits.isAnonymous && isLimitReached && limits.credits === 0 && (
        <div className="bg-gradient-to-r from-orange-50 to-yellow-50 border border-orange-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <Zap className="w-5 h-5 text-orange-500" />
            <h3 className="font-medium text-orange-800">Need More Analyses?</h3>
          </div>
          <p className="text-sm text-orange-700 mb-3">
            You&apos;ve used all your free analyses for today. Purchase credits to continue with advanced features.
          </p>
          <button
            onClick={() => alert('Credit purchase feature coming soon!')}
            className="bg-orange-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-orange-700 transition-colors"
          >
            Buy Credits
          </button>
        </div>
      )}

      {/* Feature Limitations */}
      {(limits.isAnonymous || !limits.canUseAdvanced) && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h4 className="font-medium text-gray-800 mb-2">Free Tier Limitations</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Only basic analysis available</li>
            <li>• Age estimation: Low detail only</li>
            <li>• Emotion detection: Simple analysis only</li>
            {limits.isAnonymous && <li>• Limited to 1 analysis per day</li>}
            {!limits.isAnonymous && <li>• Limited to 3 analyses per day</li>}
          </ul>
        </div>
      )}

      {/* Auth Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        initialMode="signup"
      />
    </div>
  )
}
